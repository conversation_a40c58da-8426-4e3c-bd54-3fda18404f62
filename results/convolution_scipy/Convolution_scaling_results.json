[{"input_size": 20, "algorithm_name": "Convolution", "timestamp": 1753135747.7717688, "execution_time_ms": 0.08185999759007245, "setup_time_ms": 0.03609999839682132, "cleanup_time_ms": 48.51889998826664, "total_time_ms": 48.63685998425353, "baseline_memory_mb": 503.6484375, "peak_memory_mb": 503.8984375, "memory_increment_mb": 0.25, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "20×20", "kernel_size": "5×5", "output_size": "16×16", "convolution_type": "scipy", "input_elements": 400, "kernel_elements": 25, "output_elements": 256, "theoretical_operations": 6400, "operations_per_output": 25, "reduction_ratio": 0.64}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0244140625}, {"input_size": 40, "algorithm_name": "Convolution", "timestamp": 1753135748.1019707, "execution_time_ms": 0.13546000118367374, "setup_time_ms": 0.06940000457689166, "cleanup_time_ms": 50.195899995742366, "total_time_ms": 50.40076000150293, "baseline_memory_mb": 503.8984375, "peak_memory_mb": 503.8984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "40×40", "kernel_size": "5×5", "output_size": "36×36", "convolution_type": "scipy", "input_elements": 1600, "kernel_elements": 25, "output_elements": 1296, "theoretical_operations": 32400, "operations_per_output": 25, "reduction_ratio": 0.81}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 60, "algorithm_name": "Convolution", "timestamp": 1753135748.4411805, "execution_time_ms": 0.2599800005555153, "setup_time_ms": 0.13169999874662608, "cleanup_time_ms": 41.834499992546625, "total_time_ms": 42.22617999184877, "baseline_memory_mb": 503.8984375, "peak_memory_mb": 503.8984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "60×60", "kernel_size": "5×5", "output_size": "56×56", "convolution_type": "scipy", "input_elements": 3600, "kernel_elements": 25, "output_elements": 3136, "theoretical_operations": 78400, "operations_per_output": 25, "reduction_ratio": 0.8711111111111111}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 80, "algorithm_name": "Convolution", "timestamp": 1753135748.7597096, "execution_time_ms": 0.4764400015119463, "setup_time_ms": 0.19079999765381217, "cleanup_time_ms": 38.34540001116693, "total_time_ms": 39.01264001033269, "baseline_memory_mb": 503.8984375, "peak_memory_mb": 503.8984375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "80×80", "kernel_size": "5×5", "output_size": "76×76", "convolution_type": "scipy", "input_elements": 6400, "kernel_elements": 25, "output_elements": 5776, "theoretical_operations": 144400, "operations_per_output": 25, "reduction_ratio": 0.9025}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.09765625, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "Convolution", "timestamp": 1753135749.0475821, "execution_time_ms": 0.6746400031261146, "setup_time_ms": 0.27629999385681003, "cleanup_time_ms": 37.71010000491515, "total_time_ms": 38.66104000189807, "baseline_memory_mb": 503.8984375, "peak_memory_mb": 504.0234375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "100×100", "kernel_size": "5×5", "output_size": "96×96", "convolution_type": "scipy", "input_elements": 10000, "kernel_elements": 25, "output_elements": 9216, "theoretical_operations": 230400, "operations_per_output": 25, "reduction_ratio": 0.9216}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.152587890625, "efficiency_ratio": 1.220703125}, {"input_size": 120, "algorithm_name": "Convolution", "timestamp": 1753135749.3380837, "execution_time_ms": 1.031800001510419, "setup_time_ms": 0.425199992605485, "cleanup_time_ms": 38.069100002758205, "total_time_ms": 39.52609999687411, "baseline_memory_mb": 504.0234375, "peak_memory_mb": 504.1484375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "120×120", "kernel_size": "5×5", "output_size": "116×116", "convolution_type": "scipy", "input_elements": 14400, "kernel_elements": 25, "output_elements": 13456, "theoretical_operations": 336400, "operations_per_output": 25, "reduction_ratio": 0.9344444444444444}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.2197265625, "efficiency_ratio": 1.7578125}, {"input_size": 140, "algorithm_name": "Convolution", "timestamp": 1753135749.6518085, "execution_time_ms": 1.5795599989360198, "setup_time_ms": 0.5732999998144805, "cleanup_time_ms": 41.65780000039376, "total_time_ms": 43.810659999144264, "baseline_memory_mb": 504.1484375, "peak_memory_mb": 504.2734375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "140×140", "kernel_size": "5×5", "output_size": "136×136", "convolution_type": "scipy", "input_elements": 19600, "kernel_elements": 25, "output_elements": 18496, "theoretical_operations": 462400, "operations_per_output": 25, "reduction_ratio": 0.9436734693877551}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.299072265625, "efficiency_ratio": 2.392578125}, {"input_size": 160, "algorithm_name": "Convolution", "timestamp": 1753135749.9591157, "execution_time_ms": 1.7345600004773587, "setup_time_ms": 0.6939000013517216, "cleanup_time_ms": 37.5962999969488, "total_time_ms": 40.02475999877788, "baseline_memory_mb": 504.2734375, "peak_memory_mb": 504.3984375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "160×160", "kernel_size": "5×5", "output_size": "156×156", "convolution_type": "scipy", "input_elements": 25600, "kernel_elements": 25, "output_elements": 24336, "theoretical_operations": 608400, "operations_per_output": 25, "reduction_ratio": 0.950625}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.390625, "efficiency_ratio": 3.125}, {"input_size": 180, "algorithm_name": "Convolution", "timestamp": 1753135750.257468, "execution_time_ms": 2.004919995670207, "setup_time_ms": 0.640199999907054, "cleanup_time_ms": 42.781798998476006, "total_time_ms": 45.42691899405327, "baseline_memory_mb": 504.3984375, "peak_memory_mb": 504.5234375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "180×180", "kernel_size": "5×5", "output_size": "176×176", "convolution_type": "scipy", "input_elements": 32400, "kernel_elements": 25, "output_elements": 30976, "theoretical_operations": 774400, "operations_per_output": 25, "reduction_ratio": 0.9560493827160493}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.494384765625, "efficiency_ratio": 3.955078125}, {"input_size": 200, "algorithm_name": "Convolution", "timestamp": 1753135750.5526445, "execution_time_ms": 2.6836799981538206, "setup_time_ms": 0.788499994087033, "cleanup_time_ms": 42.53859900927637, "total_time_ms": 46.01077900151722, "baseline_memory_mb": 504.5234375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "200×200", "kernel_size": "5×5", "output_size": "196×196", "convolution_type": "scipy", "input_elements": 40000, "kernel_elements": 25, "output_elements": 38416, "theoretical_operations": 960400, "operations_per_output": 25, "reduction_ratio": 0.9604}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.6103515625, "efficiency_ratio": 4.8828125}]