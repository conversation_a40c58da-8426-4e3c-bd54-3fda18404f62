[{"input_size": 20, "algorithm_name": "Convolution", "timestamp": 1753135750.865632, "execution_time_ms": 1.6533800022443756, "setup_time_ms": 0.04129999433644116, "cleanup_time_ms": 42.741299999761395, "total_time_ms": 44.43597999634221, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "20×20", "kernel_size": "5×5", "output_size": "16×16", "convolution_type": "numpy", "input_elements": 400, "kernel_elements": 25, "output_elements": 256, "theoretical_operations": 6400, "operations_per_output": 25, "reduction_ratio": 0.64}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.006103515625, "efficiency_ratio": 0.0}, {"input_size": 40, "algorithm_name": "Convolution", "timestamp": 1753135751.1859264, "execution_time_ms": 5.961119997664355, "setup_time_ms": 0.05489999603014439, "cleanup_time_ms": 41.211500007193536, "total_time_ms": 47.227520000888035, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "40×40", "kernel_size": "5×5", "output_size": "36×36", "convolution_type": "numpy", "input_elements": 1600, "kernel_elements": 25, "output_elements": 1296, "theoretical_operations": 32400, "operations_per_output": 25, "reduction_ratio": 0.81}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.0244140625, "efficiency_ratio": 0.0}, {"input_size": 60, "algorithm_name": "Convolution", "timestamp": 1753135751.5259857, "execution_time_ms": 15.488939796341583, "setup_time_ms": 0.09970000246539712, "cleanup_time_ms": 50.95279900706373, "total_time_ms": 66.54143880587071, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "60×60", "kernel_size": "5×5", "output_size": "56×56", "convolution_type": "numpy", "input_elements": 3600, "kernel_elements": 25, "output_elements": 3136, "theoretical_operations": 78400, "operations_per_output": 25, "reduction_ratio": 0.8711111111111111}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.054931640625, "efficiency_ratio": 0.0}, {"input_size": 80, "algorithm_name": "Convolution", "timestamp": 1753135751.9416497, "execution_time_ms": 27.220899995882064, "setup_time_ms": 0.19860001339111477, "cleanup_time_ms": 47.597899989341386, "total_time_ms": 75.01739999861456, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "80×80", "kernel_size": "5×5", "output_size": "76×76", "convolution_type": "numpy", "input_elements": 6400, "kernel_elements": 25, "output_elements": 5776, "theoretical_operations": 144400, "operations_per_output": 25, "reduction_ratio": 0.9025}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.09765625, "efficiency_ratio": 0.0}, {"input_size": 100, "algorithm_name": "Convolution", "timestamp": 1753135752.4874752, "execution_time_ms": 65.67973940109368, "setup_time_ms": 0.3192000003764406, "cleanup_time_ms": 71.47029899351764, "total_time_ms": 137.46923839498777, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "100×100", "kernel_size": "5×5", "output_size": "96×96", "convolution_type": "numpy", "input_elements": 10000, "kernel_elements": 25, "output_elements": 9216, "theoretical_operations": 230400, "operations_per_output": 25, "reduction_ratio": 0.9216}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.152587890625, "efficiency_ratio": 0.0}, {"input_size": 120, "algorithm_name": "Convolution", "timestamp": 1753135753.366778, "execution_time_ms": 73.7503992015263, "setup_time_ms": 0.29960001120343804, "cleanup_time_ms": 55.12419999286067, "total_time_ms": 129.1741992055904, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "120×120", "kernel_size": "5×5", "output_size": "116×116", "convolution_type": "numpy", "input_elements": 14400, "kernel_elements": 25, "output_elements": 13456, "theoretical_operations": 336400, "operations_per_output": 25, "reduction_ratio": 0.9344444444444444}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.2197265625, "efficiency_ratio": 0.0}, {"input_size": 140, "algorithm_name": "Convolution", "timestamp": 1753135754.3539174, "execution_time_ms": 103.95347900048364, "setup_time_ms": 0.41730000521056354, "cleanup_time_ms": 74.86889899882954, "total_time_ms": 179.23967800452374, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "140×140", "kernel_size": "5×5", "output_size": "136×136", "convolution_type": "numpy", "input_elements": 19600, "kernel_elements": 25, "output_elements": 18496, "theoretical_operations": 462400, "operations_per_output": 25, "reduction_ratio": 0.9436734693877551}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.299072265625, "efficiency_ratio": 0.0}, {"input_size": 160, "algorithm_name": "Convolution", "timestamp": 1753135755.5457928, "execution_time_ms": 124.55945899710059, "setup_time_ms": 0.5784000095445663, "cleanup_time_ms": 44.87009999866132, "total_time_ms": 170.00795900530647, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "160×160", "kernel_size": "5×5", "output_size": "156×156", "convolution_type": "numpy", "input_elements": 25600, "kernel_elements": 25, "output_elements": 24336, "theoretical_operations": 608400, "operations_per_output": 25, "reduction_ratio": 0.950625}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.390625, "efficiency_ratio": 0.0}, {"input_size": 180, "algorithm_name": "Convolution", "timestamp": 1753135756.8148496, "execution_time_ms": 210.3542978002224, "setup_time_ms": 0.7578000077046454, "cleanup_time_ms": 49.832698990940116, "total_time_ms": 260.9447967988672, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "180×180", "kernel_size": "5×5", "output_size": "176×176", "convolution_type": "numpy", "input_elements": 32400, "kernel_elements": 25, "output_elements": 30976, "theoretical_operations": 774400, "operations_per_output": 25, "reduction_ratio": 0.9560493827160493}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.494384765625, "efficiency_ratio": 0.0}, {"input_size": 200, "algorithm_name": "Convolution", "timestamp": 1753135758.6729965, "execution_time_ms": 220.27249800448772, "setup_time_ms": 0.8227000071201473, "cleanup_time_ms": 124.03239800187293, "total_time_ms": 345.1275960134808, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.6484375, "memory_increment_mb": 0.0, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "200×200", "kernel_size": "5×5", "output_size": "196×196", "convolution_type": "numpy", "input_elements": 40000, "kernel_elements": 25, "output_elements": 38416, "theoretical_operations": 960400, "operations_per_output": 25, "reduction_ratio": 0.9604}, "theoretical_time_complexity": "O(N² × K²)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.6103515625, "efficiency_ratio": 0.0}]