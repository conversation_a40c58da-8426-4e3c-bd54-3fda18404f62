[{"input_size": 50, "algorithm_name": "Convolution", "timestamp": 1753135760.9093585, "execution_time_ms": 0.43986000237055123, "setup_time_ms": 0.08540000999346375, "cleanup_time_ms": 41.177600010996684, "total_time_ms": 41.7028600233607, "baseline_memory_mb": 504.6484375, "peak_memory_mb": 504.8984375, "memory_increment_mb": 0.25, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "50×50", "kernel_size": "5×5", "output_size": "46×46", "convolution_type": "fft", "input_elements": 2500, "kernel_elements": 25, "output_elements": 2116, "theoretical_operations": 87157, "operations_per_output": 40, "reduction_ratio": 0.8464}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.057220458984375, "efficiency_ratio": 0.2288818359375}, {"input_size": 100, "algorithm_name": "Convolution", "timestamp": 1753135761.2798727, "execution_time_ms": 0.791040001786314, "setup_time_ms": 0.21410000044852495, "cleanup_time_ms": 40.48759899160359, "total_time_ms": 41.49273899383843, "baseline_memory_mb": 504.8984375, "peak_memory_mb": 505.1484375, "memory_increment_mb": 0.25, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "100×100", "kernel_size": "5×5", "output_size": "96×96", "convolution_type": "fft", "input_elements": 10000, "kernel_elements": 25, "output_elements": 9216, "theoretical_operations": 408631, "operations_per_output": 43, "reduction_ratio": 0.9216}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.2288818359375, "efficiency_ratio": 0.91552734375}, {"input_size": 150, "algorithm_name": "Convolution", "timestamp": 1753135761.581838, "execution_time_ms": 1.404120001825504, "setup_time_ms": 0.5967999895801768, "cleanup_time_ms": 42.53359900030773, "total_time_ms": 44.53451899171341, "baseline_memory_mb": 505.1484375, "peak_memory_mb": 506.3984375, "memory_increment_mb": 1.25, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "150×150", "kernel_size": "5×5", "output_size": "146×146", "convolution_type": "fft", "input_elements": 22500, "kernel_elements": 25, "output_elements": 21316, "theoretical_operations": 998390, "operations_per_output": 45, "reduction_ratio": 0.9473777777777778}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.514984130859375, "efficiency_ratio": 0.4119873046875}, {"input_size": 200, "algorithm_name": "Convolution", "timestamp": 1753135761.88439, "execution_time_ms": 2.785980000044219, "setup_time_ms": 1.2375999940559268, "cleanup_time_ms": 41.46989999571815, "total_time_ms": 45.4934799898183, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 508.296875, "memory_increment_mb": 4.05859375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "200×200", "kernel_size": "5×5", "output_size": "196×196", "convolution_type": "fft", "input_elements": 40000, "kernel_elements": 25, "output_elements": 38416, "theoretical_operations": 1874525, "operations_per_output": 47, "reduction_ratio": 0.9604}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 0.91552734375, "efficiency_ratio": 0.22557747834456207}, {"input_size": 250, "algorithm_name": "Convolution", "timestamp": 1753135762.2087395, "execution_time_ms": 4.474699997808784, "setup_time_ms": 1.3973999884910882, "cleanup_time_ms": 44.73290000169072, "total_time_ms": 50.604999987990595, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 510.71875, "memory_increment_mb": 6.48046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "250×250", "kernel_size": "5×5", "output_size": "246×246", "convolution_type": "fft", "input_elements": 62500, "kernel_elements": 25, "output_elements": 60516, "theoretical_operations": 3049669, "operations_per_output": 49, "reduction_ratio": 0.968256}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 1.430511474609375, "efficiency_ratio": 0.22074197558770345}, {"input_size": 300, "algorithm_name": "Convolution", "timestamp": 1753135762.5611627, "execution_time_ms": 5.751879996387288, "setup_time_ms": 2.0973000064259395, "cleanup_time_ms": 43.0491000006441, "total_time_ms": 50.89828000345733, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 513.67578125, "memory_increment_mb": 9.4375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "300×300", "kernel_size": "5×5", "output_size": "296×296", "convolution_type": "fft", "input_elements": 90000, "kernel_elements": 25, "output_elements": 87616, "theoretical_operations": 4533562, "operations_per_output": 50, "reduction_ratio": 0.9735111111111111}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 2.0599365234375, "efficiency_ratio": 0.21827141970198677}, {"input_size": 350, "algorithm_name": "Convolution", "timestamp": 1753135762.9122713, "execution_time_ms": 8.162939999601804, "setup_time_ms": 3.7579000054392964, "cleanup_time_ms": 47.45909999473952, "total_time_ms": 59.37993999978062, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 517.1640625, "memory_increment_mb": 12.92578125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "350×350", "kernel_size": "5×5", "output_size": "346×346", "convolution_type": "fft", "input_elements": 122500, "kernel_elements": 25, "output_elements": 119716, "theoretical_operations": 6334140, "operations_per_output": 51, "reduction_ratio": 0.9772734693877551}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 2.803802490234375, "efficiency_ratio": 0.21691551450589303}, {"input_size": 400, "algorithm_name": "Convolution", "timestamp": 1753135763.3136601, "execution_time_ms": 9.534739993978292, "setup_time_ms": 4.377099990961142, "cleanup_time_ms": 41.06020000472199, "total_time_ms": 54.972039989661425, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 521.0859375, "memory_increment_mb": 16.84765625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "400×400", "kernel_size": "5×5", "output_size": "396×396", "convolution_type": "fft", "input_elements": 160000, "kernel_elements": 25, "output_elements": 156816, "theoretical_operations": 8458101, "operations_per_output": 52, "reduction_ratio": 0.9801}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 3.662109375, "efficiency_ratio": 0.2173661024808718}, {"input_size": 450, "algorithm_name": "Convolution", "timestamp": 1753135763.7300053, "execution_time_ms": 15.025219798553735, "setup_time_ms": 5.036699993070215, "cleanup_time_ms": 42.72999899694696, "total_time_ms": 62.79191878857091, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 525.66015625, "memory_increment_mb": 21.421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "450×450", "kernel_size": "5×5", "output_size": "446×446", "convolution_type": "fft", "input_elements": 202500, "kernel_elements": 25, "output_elements": 198916, "theoretical_operations": 10911244, "operations_per_output": 53, "reduction_ratio": 0.9823012345679012}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 4.634857177734375, "efficiency_ratio": 0.2163609477571116}, {"input_size": 500, "algorithm_name": "Convolution", "timestamp": 1753135764.1635501, "execution_time_ms": 15.55804000236094, "setup_time_ms": 7.024599995929748, "cleanup_time_ms": 39.918299997225404, "total_time_ms": 62.50093999551609, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 530.7734375, "memory_increment_mb": 26.53515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "500×500", "kernel_size": "5×5", "output_size": "496×496", "convolution_type": "fft", "input_elements": 250000, "kernel_elements": 25, "output_elements": 246016, "theoretical_operations": 13698676, "operations_per_output": 54, "reduction_ratio": 0.984064}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 5.7220458984375, "efficiency_ratio": 0.21564018106874724}, {"input_size": 550, "algorithm_name": "Convolution", "timestamp": 1753135764.6304312, "execution_time_ms": 21.38193960126955, "setup_time_ms": 8.326900002430193, "cleanup_time_ms": 41.61359999852721, "total_time_ms": 71.32243960222695, "baseline_memory_mb": 504.23828125, "peak_memory_mb": 536.50390625, "memory_increment_mb": 32.265625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "550×550", "kernel_size": "5×5", "output_size": "546×546", "convolution_type": "fft", "input_elements": 302500, "kernel_elements": 25, "output_elements": 298116, "theoretical_operations": 16824967, "operations_per_output": 55, "reduction_ratio": 0.985507438016529}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 6.923675537109375, "efficiency_ratio": 0.21458364860774817}, {"input_size": 600, "algorithm_name": "Convolution", "timestamp": 1753135765.1180816, "execution_time_ms": 21.21457979956176, "setup_time_ms": 10.40600000123959, "cleanup_time_ms": 40.82629999902565, "total_time_ms": 72.446879799827, "baseline_memory_mb": 504.37890625, "peak_memory_mb": 542.671875, "memory_increment_mb": 38.29296875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "600×600", "kernel_size": "5×5", "output_size": "596×596", "convolution_type": "fft", "input_elements": 360000, "kernel_elements": 25, "output_elements": 355216, "theoretical_operations": 20294248, "operations_per_output": 56, "reduction_ratio": 0.9867111111111111}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 8.23974609375, "efficiency_ratio": 0.21517647658879935}, {"input_size": 650, "algorithm_name": "Convolution", "timestamp": 1753135765.6251202, "execution_time_ms": 31.900539802154526, "setup_time_ms": 11.946700004045852, "cleanup_time_ms": 40.856899999198504, "total_time_ms": 84.70413980539888, "baseline_memory_mb": 504.3828125, "peak_memory_mb": 549.37109375, "memory_increment_mb": 44.98828125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "650×650", "kernel_size": "5×5", "output_size": "646×646", "convolution_type": "fft", "input_elements": 422500, "kernel_elements": 25, "output_elements": 417316, "theoretical_operations": 24110290, "operations_per_output": 56, "reduction_ratio": 0.9877301775147929}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 9.670257568359375, "efficiency_ratio": 0.2149505893461839}, {"input_size": 700, "algorithm_name": "Convolution", "timestamp": 1753135766.1920462, "execution_time_ms": 29.379139799857512, "setup_time_ms": 11.9328000000678, "cleanup_time_ms": 39.04940000211354, "total_time_ms": 80.36133980203886, "baseline_memory_mb": 504.40625, "peak_memory_mb": 556.55859375, "memory_increment_mb": 52.15234375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "700×700", "kernel_size": "5×5", "output_size": "696×696", "convolution_type": "fft", "input_elements": 490000, "kernel_elements": 25, "output_elements": 484416, "theoretical_operations": 28276560, "operations_per_output": 57, "reduction_ratio": 0.988604081632653}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 11.2152099609375, "efficiency_ratio": 0.21504709385064788}, {"input_size": 750, "algorithm_name": "Convolution", "timestamp": 1753135766.7550354, "execution_time_ms": 38.416200000210665, "setup_time_ms": 12.67139999254141, "cleanup_time_ms": 50.92529999092221, "total_time_ms": 102.01289998367429, "baseline_memory_mb": 504.34375, "peak_memory_mb": 564.20703125, "memory_increment_mb": 59.86328125, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "750×750", "kernel_size": "5×5", "output_size": "746×746", "convolution_type": "fft", "input_elements": 562500, "kernel_elements": 25, "output_elements": 556516, "theoretical_operations": 32796270, "operations_per_output": 57, "reduction_ratio": 0.9893617777777778}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 12.874603271484375, "efficiency_ratio": 0.21506678221859707}, {"input_size": 800, "algorithm_name": "Convolution", "timestamp": 1753135767.4311137, "execution_time_ms": 43.835400001262315, "setup_time_ms": 27.684799002599902, "cleanup_time_ms": 48.42890000145417, "total_time_ms": 119.94909900531638, "baseline_memory_mb": 504.32421875, "peak_memory_mb": 572.515625, "memory_increment_mb": 68.19140625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "800×800", "kernel_size": "5×5", "output_size": "796×796", "convolution_type": "fft", "input_elements": 640000, "kernel_elements": 25, "output_elements": 633616, "theoretical_operations": 37672407, "operations_per_output": 58, "reduction_ratio": 0.990025}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 14.6484375, "efficiency_ratio": 0.21481354184567794}, {"input_size": 850, "algorithm_name": "Convolution", "timestamp": 1753135768.182965, "execution_time_ms": 61.832619601045735, "setup_time_ms": 25.10109900322277, "cleanup_time_ms": 39.423900001565926, "total_time_ms": 126.35761860583443, "baseline_memory_mb": 504.328125, "peak_memory_mb": 581.30859375, "memory_increment_mb": 76.98046875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "850×850", "kernel_size": "5×5", "output_size": "846×846", "convolution_type": "fft", "input_elements": 722500, "kernel_elements": 25, "output_elements": 715716, "theoretical_operations": 42907767, "operations_per_output": 58, "reduction_ratio": 0.9906103806228373}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 16.536712646484375, "efficiency_ratio": 0.2148169907900746}, {"input_size": 900, "algorithm_name": "Convolution", "timestamp": 1753135769.1315858, "execution_time_ms": 71.34220019506756, "setup_time_ms": 16.878000009455718, "cleanup_time_ms": 53.556400001980364, "total_time_ms": 141.77660020650364, "baseline_memory_mb": 504.3984375, "peak_memory_mb": 590.68359375, "memory_increment_mb": 86.28515625, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "900×900", "kernel_size": "5×5", "output_size": "896×896", "convolution_type": "fft", "input_elements": 810000, "kernel_elements": 25, "output_elements": 802816, "theoretical_operations": 48504976, "operations_per_output": 59, "reduction_ratio": 0.9911308641975308}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 18.5394287109375, "efficiency_ratio": 0.21486231834849925}, {"input_size": 950, "algorithm_name": "Convolution", "timestamp": 1753135770.0574467, "execution_time_ms": 82.96232039574534, "setup_time_ms": 24.319000003742985, "cleanup_time_ms": 38.306200003717095, "total_time_ms": 145.58752040320542, "baseline_memory_mb": 504.33203125, "peak_memory_mb": 600.578125, "memory_increment_mb": 96.24609375, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "950×950", "kernel_size": "5×5", "output_size": "946×946", "convolution_type": "fft", "input_elements": 902500, "kernel_elements": 25, "output_elements": 894916, "theoretical_operations": 54466508, "operations_per_output": 59, "reduction_ratio": 0.991596675900277}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 20.656585693359375, "efficiency_ratio": 0.21462258766589554}, {"input_size": 1000, "algorithm_name": "Convolution", "timestamp": 1753135771.1641805, "execution_time_ms": 96.06738019792829, "setup_time_ms": 22.679200003040023, "cleanup_time_ms": 45.18129999632947, "total_time_ms": 163.92788019729778, "baseline_memory_mb": 504.3984375, "peak_memory_mb": 611.140625, "memory_increment_mb": 106.7421875, "gpu_memory_mb": 0.0, "operations_count": null, "accuracy": null, "throughput": null, "custom_metrics": {"input_size": "1000×1000", "kernel_size": "5×5", "output_size": "996×996", "convolution_type": "fft", "input_elements": 1000000, "kernel_elements": 25, "output_elements": 992016, "theoretical_operations": 60794705, "operations_per_output": 60, "reduction_ratio": 0.992016}, "theoretical_time_complexity": "O(N² log N)", "theoretical_space_complexity": "O(N²)", "theoretical_memory_mb": 22.88818359375, "efficiency_ratio": 0.21442490668228062}]