"""
Unified Scaling Analysis Framework for DNN and Algorithm Complexity Research

This module provides a common framework for measuring time and space complexity
across different algorithms and neural network models.
"""

import time
import gc
import csv
import psutil
import os
import numpy as np

# Optional torch import
try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from contextlib import contextmanager
import threading
import json
from pathlib import Path

@dataclass
class ScalingMetrics:
    """Container for scaling analysis metrics"""
    
    # Basic parameters
    input_size: int
    algorithm_name: str
    timestamp: float = field(default_factory=time.time)
    
    # Time complexity metrics
    execution_time_ms: float = 0.0
    setup_time_ms: float = 0.0
    cleanup_time_ms: float = 0.0
    total_time_ms: float = 0.0
    
    # Space complexity metrics (in MB)
    baseline_memory_mb: float = 0.0
    peak_memory_mb: float = 0.0
    memory_increment_mb: float = 0.0
    gpu_memory_mb: float = 0.0
    
    # Algorithm-specific metrics
    operations_count: Optional[int] = None
    accuracy: Optional[float] = None
    throughput: Optional[float] = None
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # Theoretical analysis
    theoretical_time_complexity: Optional[str] = None
    theoretical_space_complexity: Optional[str] = None
    theoretical_memory_mb: float = 0.0
    efficiency_ratio: float = 0.0

class MemoryTracker:
    """Thread-safe memory usage tracker"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self._lock = threading.Lock()
        
    def get_cpu_memory_mb(self) -> float:
        """Get current CPU memory usage in MB"""
        with self._lock:
            return self.process.memory_info().rss / (1024 * 1024)
    
    def get_gpu_memory_mb(self) -> float:
        """Get current GPU memory usage in MB"""
        if HAS_TORCH and torch.cuda.is_available():
            return torch.cuda.memory_allocated() / (1024 * 1024)
        return 0.0
    
    def clear_gpu_cache(self):
        """Clear GPU memory cache"""
        if HAS_TORCH and torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

class Timer:
    """High-precision timer with warmup and multiple runs support"""
    
    def __init__(self, warmup_runs: int = 3, measurement_runs: int = 5):
        self.warmup_runs = warmup_runs
        self.measurement_runs = measurement_runs
        
    @contextmanager
    def measure(self):
        """Context manager for measuring execution time"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            end_time = time.perf_counter()
            self.last_measurement = (end_time - start_time) * 1000  # Convert to ms
    
    def measure_function(self, func: Callable, *args, **kwargs) -> Dict[str, float]:
        """Measure function execution with warmup and multiple runs"""
        
        # Warmup runs
        for _ in range(self.warmup_runs):
            func(*args, **kwargs)
        
        # Measurement runs
        times = []
        for _ in range(self.measurement_runs):
            gc.collect()
            start_time = time.perf_counter()
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)
        
        return {
            'mean_ms': np.mean(times),
            'std_ms': np.std(times),
            'min_ms': np.min(times),
            'max_ms': np.max(times),
            'median_ms': np.median(times),
            'cv': np.std(times) / np.mean(times) if np.mean(times) > 0 else 0,
            'raw_measurements': times,
            'result': result
        }

class ScalingAnalyzer(ABC):
    """Abstract base class for scaling analysis implementations"""

    @staticmethod
    def _resolve_output_dir(output_dir: str) -> str:
        """
        Intelligently resolve output directory path to workspace root.

        This method automatically detects if we're running from a subdirectory
        and adjusts the path to point to the workspace root's results directory.
        """
        if output_dir.startswith("results/"):
            # Find workspace root by looking for core/ directory
            current_path = Path.cwd()

            # Walk up the directory tree to find the workspace root
            while current_path != current_path.parent:
                if (current_path / "core").exists() and (current_path / "algorithms").exists():
                    # Found workspace root
                    workspace_root = current_path
                    return str(workspace_root / output_dir)
                current_path = current_path.parent

            # If we can't find workspace root, try relative path approach
            # Check if we're in a subdirectory by looking for ../../core
            if Path("../../core").exists():
                return f"../../{output_dir}"
            elif Path("../core").exists():
                return f"../{output_dir}"

        # Return as-is if it's already an absolute path or doesn't start with "results/"
        return output_dir

    def __init__(self,
                 algorithm_name: str,
                 output_dir: str = "results",
                 enable_gpu_tracking: bool = True):
        self.algorithm_name = algorithm_name

        # Intelligently resolve output directory
        self.output_dir = self._resolve_output_dir(output_dir)
        self.enable_gpu_tracking = enable_gpu_tracking

        # Initialize components
        self.memory_tracker = MemoryTracker()
        self.timer = Timer()
        self.metrics_history: List[ScalingMetrics] = []

        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
    @abstractmethod
    def prepare_input(self, input_size: int) -> Any:
        """Prepare input data for the given size"""
        pass
    
    @abstractmethod
    def run_algorithm(self, input_data: Any) -> Any:
        """Execute the algorithm on the input data"""
        pass
    
    @abstractmethod
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return (time_complexity, space_complexity, theoretical_memory_mb)"""
        pass
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate algorithm-specific metrics (override if needed)"""
        return {}
    
    def measure_single_run(self, input_size: int) -> ScalingMetrics:
        """Measure scaling metrics for a single input size"""
        
        # Initialize metrics
        metrics = ScalingMetrics(
            input_size=input_size,
            algorithm_name=self.algorithm_name
        )
        
        # Initialize variables to avoid UnboundLocalError
        input_data = None
        algorithm_result = None
        
        # Establish baseline memory
        gc.collect()
        if self.enable_gpu_tracking:
            self.memory_tracker.clear_gpu_cache()
        
        metrics.baseline_memory_mb = self.memory_tracker.get_cpu_memory_mb()
        
        try:
            # Measure input preparation
            with self.timer.measure():
                input_data = self.prepare_input(input_size)
            metrics.setup_time_ms = self.timer.last_measurement
            
            # Measure peak memory after input preparation
            memory_after_setup = self.memory_tracker.get_cpu_memory_mb()
            
            # Measure algorithm execution
            timing_results = self.timer.measure_function(self.run_algorithm, input_data)
            metrics.execution_time_ms = timing_results['mean_ms']
            
            # Get algorithm result for custom metrics
            algorithm_result = timing_results['result']
            
            # Measure peak memory
            metrics.peak_memory_mb = self.memory_tracker.get_cpu_memory_mb()
            if self.enable_gpu_tracking:
                metrics.gpu_memory_mb = self.memory_tracker.get_gpu_memory_mb()
            
            # Calculate memory increment
            metrics.memory_increment_mb = metrics.peak_memory_mb - metrics.baseline_memory_mb
            
            # Get theoretical analysis
            time_comp, space_comp, theoretical_mem = self.get_theoretical_complexity(input_size)
            metrics.theoretical_time_complexity = time_comp
            metrics.theoretical_space_complexity = space_comp
            metrics.theoretical_memory_mb = theoretical_mem
            
            # Calculate efficiency ratio
            if theoretical_mem > 0 and metrics.memory_increment_mb > 0:
                metrics.efficiency_ratio = theoretical_mem / metrics.memory_increment_mb
            else:
                metrics.efficiency_ratio = 0.0
            
            # Calculate custom metrics (only if both input_data and algorithm_result are available)
            if input_data is not None and algorithm_result is not None:
                metrics.custom_metrics = self.calculate_custom_metrics(
                    input_size, input_data, algorithm_result
                )
            
        except Exception as e:
            print(f"Error measuring input size {input_size}: {e}")
            metrics.custom_metrics['error'] = str(e)
        
        finally:
            # Cleanup measurement (safe cleanup)
            try:
                with self.timer.measure():
                    if input_data is not None:
                        del input_data
                    if algorithm_result is not None:
                        del algorithm_result
                    gc.collect()
                metrics.cleanup_time_ms = self.timer.last_measurement
            except:
                # If cleanup fails, set a minimal cleanup time
                metrics.cleanup_time_ms = 0.0
            
            # Total time
            metrics.total_time_ms = (metrics.setup_time_ms + 
                                   metrics.execution_time_ms + 
                                   metrics.cleanup_time_ms)
        
        return metrics
    
    def run_scaling_analysis(self, 
                           input_sizes: List[int],
                           save_results: bool = True) -> List[ScalingMetrics]:
        """Run complete scaling analysis across multiple input sizes"""
        
        print(f"Starting scaling analysis for {self.algorithm_name}")
        print(f"Input sizes: {len(input_sizes)} points from {min(input_sizes)} to {max(input_sizes)}")
        print("=" * 80)
        
        self.metrics_history.clear()
        
        for i, input_size in enumerate(input_sizes):
            print(f"Progress: {i+1}/{len(input_sizes)} - Input size: {input_size}")
            
            metrics = self.measure_single_run(input_size)
            self.metrics_history.append(metrics)
            
            # Print progress
            print(f"  Time: {metrics.execution_time_ms:.2f}ms, "
                  f"Memory: +{metrics.memory_increment_mb:.2f}MB, "
                  f"Efficiency: {metrics.efficiency_ratio:.2f}")
            
        if save_results:
            self.save_results()
            
        print(f"\nScaling analysis completed for {self.algorithm_name}")
        return self.metrics_history
    
    def save_results(self):
        """Save analysis results to CSV and JSON"""
        
        # Save to CSV
        csv_filename = os.path.join(self.output_dir, f"{self.algorithm_name}_scaling_results.csv")
        
        with open(csv_filename, 'w', newline='') as csvfile:
            if not self.metrics_history:
                return
                
            # Create fieldnames from first metrics object
            fieldnames = [
                'input_size', 'algorithm_name', 'timestamp',
                'execution_time_ms', 'setup_time_ms', 'cleanup_time_ms', 'total_time_ms',
                'baseline_memory_mb', 'peak_memory_mb', 'memory_increment_mb', 'gpu_memory_mb',
                'operations_count', 'accuracy', 'throughput',
                'theoretical_time_complexity', 'theoretical_space_complexity', 
                'theoretical_memory_mb', 'efficiency_ratio'
            ]
            
            # Add custom metrics fieldnames
            if self.metrics_history[0].custom_metrics:
                custom_fields = [f"custom_{k}" for k in self.metrics_history[0].custom_metrics.keys()]
                fieldnames.extend(custom_fields)
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for metrics in self.metrics_history:
                row = {
                    'input_size': metrics.input_size,
                    'algorithm_name': metrics.algorithm_name,
                    'timestamp': metrics.timestamp,
                    'execution_time_ms': f"{metrics.execution_time_ms:.4f}",
                    'setup_time_ms': f"{metrics.setup_time_ms:.4f}",
                    'cleanup_time_ms': f"{metrics.cleanup_time_ms:.4f}",
                    'total_time_ms': f"{metrics.total_time_ms:.4f}",
                    'baseline_memory_mb': f"{metrics.baseline_memory_mb:.2f}",
                    'peak_memory_mb': f"{metrics.peak_memory_mb:.2f}",
                    'memory_increment_mb': f"{metrics.memory_increment_mb:.2f}",
                    'gpu_memory_mb': f"{metrics.gpu_memory_mb:.2f}",
                    'operations_count': metrics.operations_count,
                    'accuracy': f"{metrics.accuracy:.4f}" if metrics.accuracy else "",
                    'throughput': f"{metrics.throughput:.4f}" if metrics.throughput else "",
                    'theoretical_time_complexity': metrics.theoretical_time_complexity,
                    'theoretical_space_complexity': metrics.theoretical_space_complexity,
                    'theoretical_memory_mb': f"{metrics.theoretical_memory_mb:.2f}",
                    'efficiency_ratio': f"{metrics.efficiency_ratio:.4f}"
                }
                
                # Add custom metrics
                for k, v in metrics.custom_metrics.items():
                    row[f"custom_{k}"] = v
                
                writer.writerow(row)
        
        # Save to JSON for more detailed analysis
        json_filename = os.path.join(self.output_dir, f"{self.algorithm_name}_scaling_results.json")
        
        # Custom JSON serializer to handle numpy types and other non-serializable objects
        def convert_for_json(obj):
            """Convert object to JSON-serializable format"""
            if isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif hasattr(obj, '__dict__'):
                return vars(obj)
            return obj
        
        # Prepare data for JSON serialization
        json_data = []
        for metrics in self.metrics_history:
            metrics_dict = vars(metrics).copy()
            # Convert custom_metrics to ensure JSON compatibility
            if 'custom_metrics' in metrics_dict:
                converted_custom = {}
                for k, v in metrics_dict['custom_metrics'].items():
                    converted_custom[k] = convert_for_json(v)
                metrics_dict['custom_metrics'] = converted_custom
            json_data.append(metrics_dict)
        
        with open(json_filename, 'w') as jsonfile:
            json.dump(json_data, jsonfile, indent=2, default=convert_for_json)
        
        print(f"Results saved to: {csv_filename} and {json_filename}")
    
    def analyze_scaling_behavior(self) -> Dict[str, float]:
        """Analyze empirical scaling behavior"""
        if len(self.metrics_history) < 2:
            return {}
        
        scaling_factors = []
        for i in range(1, len(self.metrics_history)):
            prev = self.metrics_history[i-1]
            curr = self.metrics_history[i]
            
            size_ratio = curr.input_size / prev.input_size
            time_ratio = curr.execution_time_ms / prev.execution_time_ms
            
            if size_ratio > 1 and time_ratio > 0:
                scaling_factor = np.log(time_ratio) / np.log(size_ratio)
                scaling_factors.append(scaling_factor)
        
        if scaling_factors:
            return {
                'mean_scaling_factor': np.mean(scaling_factors),
                'std_scaling_factor': np.std(scaling_factors),
                'scaling_factors': scaling_factors
            }
        
        return {} 