"""
SpMV (Sparse Matrix-Vector Multiplication) scaling analysis
"""


import sys
from pathlib import Path

# Add workspace root to Python path
workspace_root = Path(__file__).parent.parent
sys.path.insert(0, str(workspace_root))

import sys
import os
from pathlib import Path

# Add workspace root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import numpy as np
import scipy.sparse as sp
from typing import Tu<PERSON>, Dict, Any
import time
import gc

from core.scaling_framework import ScalingAnalyzer


class SpMVScalingAnalyzer(ScalingAnalyzer):
    """SpMV scaling analysis for sparse matrix-vector multiplication operations"""
    
    def __init__(self, sparsity_ratio: float = 0.01, **kwargs):
        """
        Initialize SpMV analyzer with scipy sparse implementation
        
        Args:
            sparsity_ratio: Ratio of non-zero elements (default: 0.01 = 1%)
        """
        super().__init__(algorithm_name="SpMV-scipy", **kwargs)
        self.sparsity_ratio = sparsity_ratio
    
    def prepare_input(self, input_size: int) -> Tuple[sp.csr_matrix, np.ndarray]:
        """Generate a random sparse matrix and dense vector for SpMV"""
        np.random.seed(42)  # For reproducibility
        
        # Generate sparse matrix in CSR format
        density = self.sparsity_ratio
        sparse_matrix = sp.random(input_size, input_size, density=density, 
                                format='csr', dtype=np.float64)
        
        # Generate dense vector
        vector = np.random.randn(input_size).astype(np.float64)
        
        return sparse_matrix, vector
    
    def run_algorithm(self, input_data: Tuple[sp.csr_matrix, np.ndarray]) -> np.ndarray:
        """Execute SpMV using SciPy sparse"""
        sparse_matrix, vector = input_data
        return sparse_matrix.dot(vector)
    
    def get_theoretical_complexity(self, input_size: int) -> Tuple[str, str, float]:
        """Return SpMV theoretical complexity"""
        n = input_size
        nnz = int(n * n * self.sparsity_ratio)  # Number of non-zeros
        
        # Memory: sparse matrix (nnz values + indices) + vector + result
        sparse_mb = (nnz * 8 + nnz * 4 + (n+1) * 4) / (1024 * 1024)  # CSR format
        vector_mb = (2 * n * 8) / (1024 * 1024)  # input + output vectors
        total_theoretical_mb = sparse_mb + vector_mb
        
        return (
            f"O(nnz) [N={n}, nnz≈{nnz:,}]",
            f"O(nnz+N) [N={n}, memory≈{nnz + 2*n:,} elements]",
            total_theoretical_mb
        )
    
    def calculate_custom_metrics(self, input_size: int, input_data: Any, result: Any) -> Dict[str, Any]:
        """Calculate SpMV-specific metrics"""
        sparse_matrix, vector = input_data
        
        # Matrix properties
        nnz = sparse_matrix.nnz
        actual_sparsity = nnz / (input_size * input_size)
        
        # Verify correctness by comparing with dense computation (for small matrices)
        if input_size <= 100:
            dense_matrix = sparse_matrix.toarray()
            expected_result = np.dot(dense_matrix, vector)
            max_error = np.max(np.abs(result - expected_result))
            is_correct = max_error < 1e-10
        else:
            # For large matrices, just check basic properties
            is_correct = len(result) == input_size and np.isfinite(result).all()
            max_error = 0.0
        
        # Calculate norms
        vector_norm = np.linalg.norm(vector)
        result_norm = np.linalg.norm(result)
        matrix_norm = sp.linalg.norm(sparse_matrix, 'fro')
        
        # Theoretical FLOPs: 2 * nnz (one multiply + one add per non-zero)
        theoretical_flops = 2 * nnz
        
        return {
            'correctness_verified': is_correct,
            'max_error': float(max_error),
            'operations_count': theoretical_flops,
            'algorithm_type': 'spmv_scipy',
            'matrix_size': f'{input_size}×{input_size}',
            'implementation': 'scipy_sparse',
            'nnz': int(nnz),
            'actual_sparsity_ratio': float(actual_sparsity),
            'target_sparsity_ratio': float(self.sparsity_ratio),
            'vector_norm': float(vector_norm),
            'result_norm': float(result_norm),
            'matrix_frobenius_norm': float(matrix_norm),
            'theoretical_flops': theoretical_flops,
            'storage_format': 'CSR',
            'memory_efficiency': float(nnz / (input_size * input_size))
        }


def main():
    """Run SpMV scaling analysis"""
    print("=== SpMV (Sparse Matrix-Vector Multiplication) Scaling Analysis ===")
    print("Testing SpMV performance scaling with SciPy sparse matrices")
    
    # Test different sparsity levels
    sparsity_ratios = [0.001, 0.01, 0.1]  # 0.1%, 1%, 10%
    
    for sparsity in sparsity_ratios:
        print(f"\n--- Sparsity Ratio: {sparsity*100:.1f}% ---")
        
        analyzer = SpMVScalingAnalyzer(
            sparsity_ratio=sparsity,
            output_dir=f"results/spmv_scipy_{sparsity*100:.1f}pct",
            enable_gpu_tracking=False
        )
        
        # Matrix sizes for testing
        matrix_sizes = [i for i in range(100, 5001, 200)]
        print(f"Matrix sizes: {matrix_sizes}")
        
        try:
            results = analyzer.run_scaling_analysis(matrix_sizes)
            scaling = analyzer.analyze_scaling_behavior()
            
            if scaling:
                print(f"\n=== Scaling Analysis ===")
                print(f"Mean scaling factor: {scaling['mean_scaling_factor']:.2f}")
                print(f"Standard deviation: {scaling['std_scaling_factor']:.2f}")
                print(f"Expected: ~1.0 for SpMV (O(nnz), linear in non-zeros)")
            
            print(f"\n=== Sample Results ===")
            for i, result in enumerate(results[:5]):
                custom_metrics = result.custom_metrics
                print(f"Size {result.input_size:3d}×{result.input_size:3d}: "
                      f"{result.execution_time_ms:8.2f}ms, "
                      f"Memory: +{result.memory_increment_mb:7.2f}MB, "
                      f"NNZ: {custom_metrics.get('nnz', 0):8,}, "
                      f"Correct: {custom_metrics.get('correctness_verified', False)}")
                      
        except Exception as e:
            print(f"Error during analysis: {e}")
            continue


if __name__ == "__main__":
    main()